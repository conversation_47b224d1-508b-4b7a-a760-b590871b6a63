import { supabase } from '@/integrations/supabase/client';

export interface AdminUser {
  id: string;
  bride_name: string;
  groom_name: string;
  treasurer_name: string;
  email: string;
  wedding_date: string | null;
  venue: string | null;
  treasurer_phone: string | null;
  theme: string;
  special_message: string;
  is_public: boolean;
  is_suspended: boolean;
  is_admin: boolean;
  couple_image: string | null;
  created_at: string;
  updated_at: string;
}

export interface CreateUserData {
  email: string;
  password: string;
  bride_name: string;
  groom_name: string;
  treasurer_name: string;
  wedding_date?: string;
  venue?: string;
  treasurer_phone?: string;
  theme?: string;
  special_message?: string;
}

export interface UpdateUserData {
  bride_name?: string;
  groom_name?: string;
  treasurer_name?: string;
  email?: string;
  wedding_date?: string;
  venue?: string;
  treasurer_phone?: string;
  theme?: string;
  special_message?: string;
  is_public?: boolean;
  is_suspended?: boolean;
  is_admin?: boolean;
}

/**
 * Get all users for admin management
 */
export const getAllUsers = async (): Promise<AdminUser[]> => {
  try {
    const { data: profiles, error } = await supabase
      .from('profiles')
      .select(`
        id,
        bride_name,
        groom_name,
        treasurer_name,
        email,
        wedding_date,
        venue,
        treasurer_phone,
        theme,
        special_message,
        is_public,
        is_suspended,
        is_admin,
        couple_image,
        created_at,
        updated_at
      `)
      .order('created_at', { ascending: false });

    if (error) throw error;

    return profiles || [];
  } catch (error) {
    console.error('Error fetching users:', error);
    throw error;
  }
};

/**
 * Create a new user with profile
 */
export const adminCreateUser = async (userData: CreateUserData) => {
  try {
    // 1. Create auth user
    const { data: authData, error: authError } = await supabase.auth.admin.createUser({
      email: userData.email,
      password: userData.password,
      email_confirm: true
    });

    if (authError) {
      console.error('Error creating auth user:', authError);
      throw authError;
    }

    if (!authData.user) {
      throw new Error('No user data returned from auth creation');
    }

    // 2. Create/update profile
    const profileData = {
      id: authData.user.id,
      bride_name: userData.bride_name,
      groom_name: userData.groom_name,
      treasurer_name: userData.treasurer_name,
      email: userData.email,
      wedding_date: userData.wedding_date || null,
      venue: userData.venue || null,
      treasurer_phone: userData.treasurer_phone || null,
      theme: userData.theme || 'sunset',
      special_message: userData.special_message || 'Join us in celebrating our special day with your loving support and contributions.',
      is_public: false,
      is_suspended: false,
      is_admin: false,
      updated_at: new Date().toISOString()
    };

    const { error: profileError } = await supabase
      .from('profiles')
      .upsert(profileData);

    if (profileError) {
      console.error('Error creating profile:', profileError);
      // Try to clean up auth user if profile creation fails
      await supabase.auth.admin.deleteUser(authData.user.id);
      throw profileError;
    }

    return { success: true, user: authData.user };
  } catch (error) {
    console.error('Error in admin user creation:', error);
    throw error;
  }
};

/**
 * Update user profile data
 */
export const adminUpdateUser = async (userId: string, updateData: UpdateUserData) => {
  try {
    const { error } = await supabase
      .from('profiles')
      .update({
        ...updateData,
        updated_at: new Date().toISOString()
      })
      .eq('id', userId);

    if (error) {
      console.error('Error updating user:', error);
      throw error;
    }

    return { success: true };
  } catch (error) {
    console.error('Error in admin user update:', error);
    throw error;
  }
};

/**
 * Suspend or unsuspend a user
 */
export const adminSuspendUser = async (userId: string, suspend: boolean) => {
  try {
    const { error } = await supabase
      .from('profiles')
      .update({
        is_suspended: suspend,
        updated_at: new Date().toISOString()
      })
      .eq('id', userId);

    if (error) {
      console.error('Error suspending user:', error);
      throw error;
    }

    return { success: true };
  } catch (error) {
    console.error('Error in admin user suspension:', error);
    throw error;
  }
};

/**
 * Delete a user and all associated data using Supabase Edge Function
 */
export const adminDeleteUser = async (userId: string) => {
  try {
    // Get the current session to include auth token
    const { data: { session } } = await supabase.auth.getSession();

    if (!session) {
      throw new Error('No active session');
    }

    // Call the edge function for admin user deletion
    const { data, error } = await supabase.functions.invoke('admin-delete-user', {
      body: { userId },
      headers: {
        Authorization: `Bearer ${session.access_token}`,
      },
    });

    if (error) {
      console.error('Error calling admin-delete-user function:', error);
      throw error;
    }

    if (!data.success) {
      throw new Error(data.error || 'Failed to delete user');
    }

    return { success: true };
  } catch (error) {
    console.error('Error in admin user deletion:', error);
    throw error;
  }
};

/**
 * Get user by ID
 */
export const getUserById = async (userId: string): Promise<AdminUser | null> => {
  try {
    const { data: profile, error } = await supabase
      .from('profiles')
      .select(`
        id,
        bride_name,
        groom_name,
        treasurer_name,
        email,
        wedding_date,
        venue,
        treasurer_phone,
        theme,
        special_message,
        is_public,
        is_suspended,
        is_admin,
        couple_image,
        created_at,
        updated_at
      `)
      .eq('id', userId)
      .single();

    if (error) {
      console.error('Error fetching user:', error);
      throw error;
    }

    return profile;
  } catch (error) {
    console.error('Error in getUserById:', error);
    throw error;
  }
};
