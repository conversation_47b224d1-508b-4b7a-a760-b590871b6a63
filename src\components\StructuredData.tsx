import React, { useEffect } from 'react';
import { generateStructuredData, injectStructuredData } from '@/utils/seo';

interface StructuredDataProps {
  type: 'Organization' | 'WebSite' | 'Event' | 'Service' | 'SoftwareApplication' | 'FAQPage';
  data?: any;
  multiple?: boolean; // For injecting multiple structured data objects
}

const StructuredData: React.FC<StructuredDataProps> = ({ type, data = {}, multiple = false }) => {
  useEffect(() => {
    if (multiple && Array.isArray(data)) {
      // Handle multiple structured data objects
      const structuredDataArray = data.map(item => 
        generateStructuredData(item.type, item.data)
      );
      injectStructuredData(structuredDataArray);
    } else {
      // Handle single structured data object
      const structuredData = generateStructuredData(type, data);
      injectStructuredData(structuredData);
    }
  }, [type, data, multiple]);

  // This component doesn't render anything visible
  return null;
};

export default StructuredData;

// Predefined structured data components for common use cases

export const OrganizationStructuredData: React.FC<{ data?: any }> = ({ data }) => (
  <StructuredData type="Organization" data={data} />
);

export const WebSiteStructuredData: React.FC<{ data?: any }> = ({ data }) => (
  <StructuredData type="WebSite" data={data} />
);

export const ServiceStructuredData: React.FC<{ data?: any }> = ({ data }) => (
  <StructuredData type="Service" data={data} />
);

export const SoftwareApplicationStructuredData: React.FC<{ data?: any }> = ({ data }) => (
  <StructuredData type="SoftwareApplication" data={data} />
);

export const EventStructuredData: React.FC<{ data?: any }> = ({ data }) => (
  <StructuredData type="Event" data={data} />
);

export const FAQStructuredData: React.FC<{ data?: any }> = ({ data }) => (
  <StructuredData type="FAQPage" data={data} />
);

// Combined structured data for the home page
export const HomePageStructuredData: React.FC = () => {
  const combinedData = [
    {
      type: 'Organization',
      data: {
        name: "PledgeForLove Uganda",
        url: "https://p4love.com",
        logo: "https://p4love.com/images/logo.png",
        description: "Digital wedding contribution platform for Ugandan weddings",
        foundingDate: "2024",
        founders: [
          {
            "@type": "Person",
            name: "PledgeForLove Team"
          }
        ],
        knowsAbout: [
          "Wedding Planning",
          "Digital Payments",
          "Event Management",
          "Ugandan Traditions"
        ],
        slogan: "Transform your wedding contributions with beautiful digital pledge cards"
      }
    },
    {
      type: 'WebSite',
      data: {
        name: "PledgeForLove Uganda",
        alternateName: "PledgeForLove",
        description: "Digital wedding contribution platform for creating and managing pledge cards",
        inLanguage: "en-UG",
        isAccessibleForFree: true,
        keywords: "Uganda wedding, wedding contributions, pledge cards, digital wedding, wedding planning"
      }
    },
    {
      type: 'SoftwareApplication',
      data: {
        name: "PledgeForLove Uganda Platform",
        description: "Web application for managing digital wedding contributions and pledge cards",
        url: "https://p4love.com",
        screenshot: "https://p4love.com/images/screenshot.jpg",
        featureList: [
          "Create digital pledge cards",
          "Track wedding contributions",
          "Manage guest pledges",
          "Real-time payment tracking",
          "Social media sharing",
          "Mobile-friendly interface"
        ],
        requirements: "Web browser with internet connection",
        permissions: "No special permissions required"
      }
    },
    {
      type: 'Service',
      data: {
        name: "Wedding Contribution Management",
        description: "Comprehensive digital solution for managing wedding contributions and guest pledges",
        serviceOutput: "Digital pledge cards and contribution tracking",
        hoursAvailable: {
          "@type": "OpeningHoursSpecification",
          dayOfWeek: [
            "Monday", "Tuesday", "Wednesday", "Thursday", 
            "Friday", "Saturday", "Sunday"
          ],
          opens: "00:00",
          closes: "23:59"
        }
      }
    }
  ];

  return <StructuredData type="Organization" data={combinedData} multiple={true} />;
};

// Breadcrumb structured data
export const BreadcrumbStructuredData: React.FC<{ 
  items: Array<{ name: string; url: string }> 
}> = ({ items }) => {
  const breadcrumbData = {
    "@context": "https://schema.org",
    "@type": "BreadcrumbList",
    itemListElement: items.map((item, index) => ({
      "@type": "ListItem",
      position: index + 1,
      name: item.name,
      item: item.url
    }))
  };

  useEffect(() => {
    injectStructuredData(breadcrumbData);
  }, [items]);

  return null;
};

// Local business structured data for Uganda-specific information
export const LocalBusinessStructuredData: React.FC = () => {
  const localBusinessData = {
    "@context": "https://schema.org",
    "@type": "LocalBusiness",
    name: "PledgeForLove Uganda",
    description: "Digital wedding contribution platform serving Uganda",
    url: "https://p4love.com",
    telephone: "+256-XXX-XXXXXX", // Replace with actual phone number
    email: "<EMAIL>",
    address: {
      "@type": "PostalAddress",
      addressCountry: "UG",
      addressLocality: "Kampala",
      addressRegion: "Central Region"
    },
    geo: {
      "@type": "GeoCoordinates",
      latitude: "0.3136",
      longitude: "32.5811"
    },
    openingHours: "Mo-Su 00:00-23:59",
    priceRange: "Free",
    currenciesAccepted: "UGX",
    paymentAccepted: "Mobile Money, Bank Transfer",
    areaServed: {
      "@type": "Country",
      name: "Uganda"
    }
  };

  useEffect(() => {
    injectStructuredData(localBusinessData);
  }, []);

  return null;
};
