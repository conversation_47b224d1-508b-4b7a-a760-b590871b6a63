-- Add admin role field to profiles table
ALTER TABLE profiles
ADD COLUMN IF NOT EXISTS is_admin BOOLEAN DEFAULT FALSE;

-- Create index for better performance on admin queries
CREATE INDEX IF NOT EXISTS idx_profiles_is_admin ON profiles(is_admin);

-- Create a function to check if current user is admin
-- This avoids the circular dependency in RLS policies
CREATE OR REPLACE FUNCTION is_admin_user()
RETURNS BOOLEAN
LANGUAGE SQL
SECURITY DEFINER
STABLE
AS $$
  SELECT EXISTS (
    SELECT 1 FROM profiles
    WHERE id = auth.uid() AND is_admin = TRUE
  );
$$;

-- Create admin-specific RLS policies using the function
-- <PERSON><PERSON> can view all profiles
CREATE POLICY "Ad<PERSON> can view all profiles"
ON profiles FOR SELECT
USING (is_admin_user());

-- <PERSON><PERSON> can update all profiles
CREATE POLICY "Ad<PERSON> can update all profiles"
ON profiles FOR UPDATE
USING (is_admin_user());

-- Ad<PERSON> can delete all profiles
CREATE POLICY "Ad<PERSON> can delete all profiles"
ON profiles FOR DELETE
USING (is_admin_user());

-- <PERSON><PERSON> can view all pledges
CREATE POLICY "<PERSON><PERSON> can view all pledges"
ON pledges FOR SELECT
USING (is_admin_user());

-- Admins can update all pledges
CREATE POLICY "Admins can update all pledges"
ON pledges FOR UPDATE
USING (is_admin_user());

-- Admins can delete all pledges
CREATE POLICY "Admins can delete all pledges"
ON pledges FOR DELETE
USING (is_admin_user());

-- Function to check if a user is admin
CREATE OR REPLACE FUNCTION is_admin(user_id UUID)
RETURNS BOOLEAN AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 FROM profiles 
    WHERE id = user_id AND is_admin = TRUE
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to promote user to admin (can only be called by existing admins)
CREATE OR REPLACE FUNCTION promote_to_admin(target_user_id UUID)
RETURNS BOOLEAN AS $$
BEGIN
  -- Check if the calling user is an admin
  IF NOT is_admin(auth.uid()) THEN
    RAISE EXCEPTION 'Only admins can promote users to admin';
  END IF;
  
  -- Promote the target user
  UPDATE profiles 
  SET is_admin = TRUE, updated_at = NOW()
  WHERE id = target_user_id;
  
  RETURN TRUE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to demote admin (can only be called by existing admins)
CREATE OR REPLACE FUNCTION demote_from_admin(target_user_id UUID)
RETURNS BOOLEAN AS $$
BEGIN
  -- Check if the calling user is an admin
  IF NOT is_admin(auth.uid()) THEN
    RAISE EXCEPTION 'Only admins can demote admin users';
  END IF;
  
  -- Prevent self-demotion if it would leave no admins
  IF target_user_id = auth.uid() THEN
    IF (SELECT COUNT(*) FROM profiles WHERE is_admin = TRUE) <= 1 THEN
      RAISE EXCEPTION 'Cannot demote the last admin user';
    END IF;
  END IF;
  
  -- Demote the target user
  UPDATE profiles 
  SET is_admin = FALSE, updated_at = NOW()
  WHERE id = target_user_id;
  
  RETURN TRUE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
