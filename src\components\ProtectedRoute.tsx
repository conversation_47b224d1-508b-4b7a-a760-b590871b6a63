import React, { useState, useEffect } from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import LoadingSpinner from '@/components/ui/loading-spinner';
import AuthErrorDisplay from '@/components/AuthErrorDisplay';
import AuthErrorBoundary from '@/components/AuthErrorBoundary';
import { isCurrentUserAdmin } from '@/utils/adminAuth';
import { Tables } from '@/integrations/supabase/types';

interface ProtectedRouteProps {
  children: React.ReactNode;
  requireProfile?: boolean;
}

/**
 * Determines if a profile is incomplete (has placeholder values)
 * @param profile - The user's profile data
 * @returns true if profile needs to be completed, false if it's complete
 */
const isProfileIncomplete = (profile: Tables<'profiles'> | null): boolean => {
  if (!profile) return true;

  // Check for placeholder values that indicate an incomplete profile
  const hasPlaceholderNames =
    profile.bride_name === 'Bride Name' ||
    profile.groom_name === 'Groom Name' ||
    profile.treasurer_name === 'Treasurer Name';

  // Check for missing required fields (even if not placeholders)
  const missingRequiredFields =
    !profile.bride_name?.trim() ||
    !profile.groom_name?.trim() ||
    !profile.treasurer_name?.trim() ||
    !profile.wedding_date;

  return hasPlaceholderNames || missingRequiredFields;
};

const ProtectedRoute: React.FC<ProtectedRouteProps> = ({
  children,
  requireProfile = false
}) => {
  const { user, profile, loading, profileLoading, error } = useAuth();
  const location = useLocation();
  const [isAdmin, setIsAdmin] = useState<boolean | null>(null);
  const [adminCheckLoading, setAdminCheckLoading] = useState(true);

  // Check admin status when user changes - PRIORITY CHECK
  useEffect(() => {
    const checkAdminStatus = async () => {
      if (!user) {
        setIsAdmin(false);
        setAdminCheckLoading(false);
        return;
      }

      try {
        const adminStatus = await isCurrentUserAdmin();
        setIsAdmin(adminStatus);
        console.log('ProtectedRoute: Admin status check result:', { userId: user.id, isAdmin: adminStatus });
      } catch (error) {
        console.error('Error checking admin status:', error);
        setIsAdmin(false);
      } finally {
        setAdminCheckLoading(false);
      }
    };

    if (!loading && !profileLoading) {
      checkAdminStatus();
    }
  }, [user, loading, profileLoading]);

  const profileIncomplete = isProfileIncomplete(profile);

  console.log('ProtectedRoute evaluation:', {
    path: location.pathname,
    user: user?.id,
    profile: profile?.id,
    profileIncomplete,
    loading,
    profileLoading,
    requireProfile,
    isAdmin,
    adminCheckLoading,
    profileData: profile ? {
      bride_name: profile.bride_name,
      groom_name: profile.groom_name,
      treasurer_name: profile.treasurer_name,
      wedding_date: profile.wedding_date
    } : null
  });

  // Show loading spinner while checking authentication or admin status
  if (loading || profileLoading || adminCheckLoading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <LoadingSpinner text="Checking authentication..." />
      </div>
    );
  }

  // Show loading spinner while profile is being fetched for new users
  if (user && requireProfile && profileLoading && profile === null) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <LoadingSpinner text="Loading profile..." />
      </div>
    );
  }

  // Redirect to auth page if not authenticated
  if (!user) {
    console.log('ProtectedRoute: No user found, redirecting to auth', {
      path: location.pathname,
      loading,
      profileLoading
    });
    return (
      <Navigate
        to="/auth"
        state={{ from: location }}
        replace
      />
    );
  }

  // Special handling for profile-setup route - prevent redirect loops
  if (location.pathname === '/profile-setup') {
    console.log('ProtectedRoute: Handling profile-setup route', {
      user: user?.id,
      profile: profile?.id,
      hasProfile: !!profile,
      profileIncomplete
    });

    // Always allow access to profile-setup page to prevent interrupting the setup process
    // Users can navigate away manually when they're done
    console.log('ProtectedRoute: Allowing access to profile-setup to prevent interrupting setup process');
    return <>{children}</>;
  }

  // PRIORITY: Check if user is admin first - admins bypass ALL profile requirements
  if (isAdmin === true) {
    console.log('ProtectedRoute: User is admin - bypassing all profile requirements');
    return <>{children}</>;
  }

  // For non-admin users: check profile requirements
  if (requireProfile && profileIncomplete && isAdmin === false) {
    console.log('ProtectedRoute: Non-admin user with incomplete profile, redirecting to profile-setup');
    return (
      <Navigate
        to="/profile-setup"
        state={{ from: location }}
        replace
      />
    );
  }

  // User is authenticated and meets requirements, render the protected content
  return (
    <AuthErrorBoundary>
      <AuthErrorDisplay />
      {children}
    </AuthErrorBoundary>
  );
};

export default ProtectedRoute;
