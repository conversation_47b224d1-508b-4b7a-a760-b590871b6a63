-- Fix infinite recursion in RLS policies
-- This migration resolves conflicts between overlapping policies

-- First, drop all existing policies on profiles table to start fresh
DROP POLICY IF EXISTS "Users can view their own profile" ON profiles;
DROP POLICY IF EXISTS "Users can update their own profile" ON profiles;
DROP POLICY IF EXISTS "Users can insert their own profile" ON profiles;
DROP POLICY IF EXISTS "Public can view public non-suspended profiles" ON profiles;
DROP POLICY IF EXISTS "Suspended users cannot access their profile" ON profiles;
DROP POLICY IF EXISTS "Ad<PERSON> can view all profiles" ON profiles;
DROP POLICY IF EXISTS "Admins can update all profiles" ON profiles;
DROP POLICY IF EXISTS "Ad<PERSON> can delete all profiles" ON profiles;

-- Create the admin check function if it doesn't exist
CREATE OR REPLACE FUNCTION is_admin_user()
RETURNS BOOLEAN
LANGUAGE SQL
SECURITY DEFINER
STABLE
AS $$
  SELECT EXISTS (
    SELECT 1 FROM profiles 
    WHERE id = auth.uid() AND is_admin = TRUE
  );
$$;

-- Create comprehensive RLS policies that handle all cases without conflicts

-- 1. Users can view their own profile (if not suspended) OR if they are admin
CREATE POLICY "Users can view own profile or admins view all"
ON profiles FOR SELECT
USING (
  (auth.uid() = id AND (is_suspended = FALSE OR is_suspended IS NULL))
  OR is_admin_user()
);

-- 2. Users can update their own profile (if not suspended) OR if they are admin
CREATE POLICY "Users can update own profile or admins update all"
ON profiles FOR UPDATE
USING (
  (auth.uid() = id AND (is_suspended = FALSE OR is_suspended IS NULL))
  OR is_admin_user()
);

-- 3. Users can insert their own profile (suspension doesn't apply to new profiles)
CREATE POLICY "Users can insert own profile"
ON profiles FOR INSERT
WITH CHECK (auth.uid() = id);

-- 4. Admins can delete profiles
CREATE POLICY "Admins can delete profiles"
ON profiles FOR DELETE
USING (is_admin_user());

-- 5. Public can view public non-suspended profiles (for pledge cards)
CREATE POLICY "Public can view public profiles"
ON profiles FOR SELECT
USING (
  is_public = TRUE 
  AND (is_suspended = FALSE OR is_suspended IS NULL)
);

-- Update pledges policies to use the same pattern
DROP POLICY IF EXISTS "Users can view their own pledges" ON pledges;
DROP POLICY IF EXISTS "Users can update their own pledges" ON pledges;
DROP POLICY IF EXISTS "Users can insert their own pledges" ON pledges;
DROP POLICY IF EXISTS "Users can delete their own pledges" ON pledges;
DROP POLICY IF EXISTS "Admins can view all pledges" ON pledges;
DROP POLICY IF EXISTS "Admins can update all pledges" ON pledges;
DROP POLICY IF EXISTS "Admins can delete all pledges" ON pledges;

-- Recreate pledges policies
CREATE POLICY "Users can view own pledges or admins view all"
ON pledges FOR SELECT
USING (
  auth.uid() = user_id
  OR is_admin_user()
);

CREATE POLICY "Users can update own pledges or admins update all"
ON pledges FOR UPDATE
USING (
  auth.uid() = user_id
  OR is_admin_user()
);

CREATE POLICY "Users can insert own pledges"
ON pledges FOR INSERT
WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can delete own pledges or admins delete all"
ON pledges FOR DELETE
USING (
  auth.uid() = user_id
  OR is_admin_user()
);

-- Grant necessary permissions
GRANT SELECT, INSERT, UPDATE, DELETE ON profiles TO authenticated;
GRANT SELECT, INSERT, UPDATE, DELETE ON pledges TO authenticated;
GRANT USAGE ON SEQUENCE profiles_id_seq TO authenticated;
GRANT USAGE ON SEQUENCE pledges_id_seq TO authenticated;
