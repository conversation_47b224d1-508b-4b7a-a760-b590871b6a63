import { supabase } from '@/integrations/supabase/client';

/**
 * Check if the current user is an admin
 */
export const isCurrentUserAdmin = async (): Promise<boolean> => {
  try {
    const { data: { session } } = await supabase.auth.getSession();
    if (!session?.user) {
      console.log('isCurrentUserAdmin: No session found');
      return false;
    }

    console.log('isCurrentUserAdmin: Checking admin status for user:', session.user.id);

    // TEMPORARY: Hardcoded admin check for specific user while fixing database
    if (session.user.email === '<EMAIL>') {
      console.log('isCurrentUserAdmin: Hardcoded admin <NAME_EMAIL>');
      return true;
    }

    const { data: profile, error } = await supabase
      .from('profiles')
      .select('is_admin')
      .eq('id', session.user.id)
      .single();

    if (error) {
      console.error('Error checking admin status:', error);
      // If column doesn't exist, user is not admin
      if (error.message?.includes('column') || error.message?.includes('does not exist')) {
        console.log('isCurrentUserAdmin: is_admin column does not exist, checking hardcoded admin');
        // Fallback to hardcoded admin check
        return session.user.email === '<EMAIL>';
      }
      return false;
    }

    const isAdmin = profile?.is_admin || false;
    console.log('isCurrentUserAdmin: Result:', { userId: session.user.id, isAdmin, profile });
    return isAdmin;
  } catch (error) {
    console.error('Error in isCurrentUserAdmin:', error);
    // Final fallback for the specific admin user
    const { data: { session } } = await supabase.auth.getSession();
    return session?.user?.email === '<EMAIL>' || false;
  }
};

/**
 * Check if a specific user is an admin
 */
export const isUserAdmin = async (userId: string): Promise<boolean> => {
  try {
    const { data: profile, error } = await supabase
      .from('profiles')
      .select('is_admin')
      .eq('id', userId)
      .single();

    if (error) {
      console.error('Error checking user admin status:', error);
      return false;
    }

    return profile?.is_admin || false;
  } catch (error) {
    console.error('Error in isUserAdmin:', error);
    return false;
  }
};

/**
 * Promote a user to admin (requires current user to be admin)
 */
export const promoteToAdmin = async (userId: string): Promise<boolean> => {
  try {
    // Check if current user is admin
    const isCurrentAdmin = await isCurrentUserAdmin();
    if (!isCurrentAdmin) {
      throw new Error('Only admins can promote users to admin');
    }

    // Update user to admin
    const { error } = await supabase
      .from('profiles')
      .update({
        is_admin: true,
        updated_at: new Date().toISOString()
      })
      .eq('id', userId);

    if (error) {
      console.error('Error promoting user to admin:', error);
      throw error;
    }

    return true;
  } catch (error) {
    console.error('Error in promoteToAdmin:', error);
    throw error;
  }
};

/**
 * Demote a user from admin (requires current user to be admin)
 */
export const demoteFromAdmin = async (userId: string): Promise<boolean> => {
  try {
    // Check if current user is admin
    const isCurrentAdmin = await isCurrentUserAdmin();
    if (!isCurrentAdmin) {
      throw new Error('Only admins can demote admin users');
    }

    // Get current user ID
    const { data: { session } } = await supabase.auth.getSession();
    if (!session?.user) {
      throw new Error('Not authenticated');
    }

    // Prevent self-demotion if it would leave no admins
    if (userId === session.user.id) {
      const { data: adminCount } = await supabase
        .from('profiles')
        .select('id', { count: 'exact' })
        .eq('is_admin', true);

      if ((adminCount?.length || 0) <= 1) {
        throw new Error('Cannot demote the last admin user');
      }
    }

    // Update user to remove admin
    const { error } = await supabase
      .from('profiles')
      .update({
        is_admin: false,
        updated_at: new Date().toISOString()
      })
      .eq('id', userId);

    if (error) {
      console.error('Error demoting user from admin:', error);
      throw error;
    }

    return true;
  } catch (error) {
    console.error('Error in demoteFromAdmin:', error);
    throw error;
  }
};

/**
 * Create the first admin user (for initial setup) - Direct database approach
 */
export const createFirstAdmin = async (email: string): Promise<boolean> => {
  try {
    // First, check if user exists
    const { data: user, error: userError } = await supabase
      .from('profiles')
      .select('id, email, is_admin')
      .eq('email', email)
      .single();

    if (userError) {
      throw new Error(`User with email ${email} not found. Please create an account first.`);
    }

    if (user.is_admin) {
      throw new Error(`User ${email} is already an admin.`);
    }

    // Update user to admin
    const { error: updateError } = await supabase
      .from('profiles')
      .update({
        is_admin: true,
        updated_at: new Date().toISOString()
      })
      .eq('email', email);

    if (updateError) {
      console.error('Error updating user to admin:', updateError);
      throw updateError;
    }

    return true;
  } catch (error) {
    console.error('Error in createFirstAdmin:', error);
    throw error;
  }
};

/**
 * Get all admin users
 */
export const getAllAdmins = async () => {
  try {
    const { data: admins, error } = await supabase
      .from('profiles')
      .select(`
        id,
        bride_name,
        groom_name,
        treasurer_name,
        email,
        created_at,
        updated_at
      `)
      .eq('is_admin', true)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error fetching admins:', error);
      throw error;
    }

    return admins || [];
  } catch (error) {
    console.error('Error in getAllAdmins:', error);
    throw error;
  }
};
